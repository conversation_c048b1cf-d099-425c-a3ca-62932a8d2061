import { DeepPartial, ObjectLiteral } from "typeorm/browser";
import { Context, createContext } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";
import { generateID } from "../../framework/helper.js";
import { DataSource } from "typeorm/browser";
import { Log as ILog } from "../gatewayimpl/table_log.js";

export interface Log {
  id: string;
  name: string;
  message: any;
  status: string; // ERROR | SUCCESS
  createdAt:	Date | null;
}

export class FindLogFilter {
  // id?: string;
  name?: string;
  status?: string;
  page?: number;
  size?: number;
}

export type FindLog = FindManyEntity<ObjectLiteral, FindLogFilter>;
export type FindOneLog = (ctx: Context, id: string) => Promise<Log | null>;

export const setLogger = (name: string, message: any, status: string): Log => {
  // 
  const logger = {
    id: generateID(16),
    name: name,
    message: message,
    status: status.toUpperCase(),
    createdAt: new Date(),
  };
  return logger;
};

export const saveLogger = async (ds: DataSource, name: string, messages: any, status: string): Promise<Log> => {
  // 
  let message = messages;
  if (typeof messages === "object" && !(messages instanceof Error)) {
    message = messages;
  } else if (messages instanceof Error) {
    message = { error: messages.message, stack: messages.stack };
  } else if (typeof messages === "string") {
    message = { text: messages };
  }

  const repo = ds.getRepository(ILog);
  const log = await repo.save({
    id: generateID(16),
    name: name,
    message: message,
    status: status.toUpperCase(),
    createdAt: new Date(),
  });

  return log;
};

import nodemailer from "nodemailer";
import { getDatasource, nodemailerTranport } from "../../index.js";
import { DocumentTemplate, SubDocumentRequisition, TypeOf, ucwords } from "../model/vo.js";
import { docTypeToName } from "./helper.js";
import { saveLogger } from "../model/model_log.js";


const baseUrlEmployee = process.env.APPLICATION_MODE && process.env.APPLICATION_MODE !== "development" ? process.env.APP_EMPLOYEE_BASE_URL : "http://localhost:3001/employee";
const mailImg = "data:image/png;base64,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";


const send = async (mailRecipients: MailRecipients & MailContent & MailHasSubmittedBy, data: MailTableData[], docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION") => {
    // 
    const ds = getDatasource();
    
    // Define the email options
    const mailOptions: MailOptions = {
        from: '<<EMAIL>>', // sender address
        to: `${mailRecipients.sendToUserMail}`, // list of receivers
        cc: mailRecipients.ccUserMail ?? [], // list of cc emails
        subject: `${mailRecipients.mailSubject}`, // Subject line
        html: mailTemplate(mailRecipients, data, docType), // html mail
    };

    const { from, html, ...logEmail } = mailOptions;

    try {
        // Create a transporter object
        const transporter = nodemailer.createTransport(nodemailerTranport());

        // Send the email
        const info = await transporter.sendMail(mailOptions);
        
        // Log the result
        console.log("Message sent successfully:", info.messageId);
        console.log("Message sent to:", info.envelope.to);
        console.log("Message sent cc:", mailRecipients.sendToUserMail);
        saveLogger(ds, "sendMail", { ...logEmail, info: info.messageId }, "SUCCESS");
    } catch (error) {
        console.error("Error occurred while sending email:", error);
        saveLogger(ds, "sendMail", { ...logEmail, info: error }, "FAILED");
    }
}

export const sendApprovalMail = (mailRecipients: MailRecipients, data: MailTableData[], docType: TypeOf<typeof DocumentTemplate>) => {
    // 
    if (!data || data.length === 0) {
        throw new Error(`approval mail data cannot be empty`);
    }

    const mailType: TypeOf<typeof MailType> = "APPROVAL";

    const recipients: MailRecipients & MailContent = {
        ...mailRecipients,
        mailTitle: getMailTitle(docType, mailType),
        mailSubject: getMailSubject(docType, mailType),
        mailBodyMessage: getMailBody(docType, mailType)
    }

    const mailData = data.map((dat) => ({
        ...dat,
        url: getMailUrlDetail(docType, dat.docId),
        tableHeader: getMailTableHeader(docType, mailType),
    }));
    
    send(recipients, mailData, docType);
}

export const sendNotificationMail = (mailRecipients: MailRecipients & MailHasSubmittedBy, data: MailTableData[], docType: TypeOf<typeof DocumentTemplate>) => {
    // 
    if (!data || data.length === 0) {
        throw new Error(`notification mail data cannot be empty`);
    }

    const mailType: TypeOf<typeof MailType> = "NOTIFICATION";

    const recipients: MailRecipients & MailHasSubmittedBy & MailContent = {
        ...mailRecipients,
        mailTitle: getMailTitle(docType, mailType),
        mailSubject: getMailSubject(docType, mailType),
        mailBodyMessage: getMailBody(docType, mailType, mailRecipients.submittedByUserName)
    }

    const mailData = data.map((dat) => ({
        ...dat,
        url: getMailUrlDetail(docType, dat.docId),
        tableHeader: getMailTableHeader(docType, mailType),
    }));

    send(recipients, mailData, docType);
}

export const sendAssignmentMail = (mailRecipients: MailRecipients & MailHasSubmittedBy, data: MailTableData[], docType: TypeOf<typeof DocumentTemplate>) => {
    // 
    if (!data || data.length === 0) {
        throw new Error(`assignment mail data cannot be empty`);
    }

    const mailType: TypeOf<typeof MailType> = "ASSIGNMENT";
    
    const recipients: MailRecipients & MailHasSubmittedBy & MailContent = {
        ...mailRecipients,
        mailTitle: getMailTitle(docType, mailType),
        mailSubject: getMailSubject(docType, mailType),
        mailBodyMessage: getMailBody(docType, mailType, mailRecipients.submittedByUserName)
    }

    const mailData = data.map((dat) => ({
        ...dat,
        url: getMailUrlDetail(docType, dat.docId),
        tableHeader: getMailTableHeader(docType, mailType),
    }));

    send(recipients, mailData, docType);
}

export const sendSendBackMail = (mailRecipients: MailRecipients, data: MailTableData[], docType: TypeOf<typeof DocumentTemplate>) => {
    // 
    if (!data || data.length === 0) {
        throw new Error(`send back mail data cannot be empty`);
    }

    const mailType: TypeOf<typeof MailType> = "SENDBACK";

    const recipients: MailRecipients & MailContent = {
        ...mailRecipients,
        mailTitle: getMailTitle(docType, mailType),
        mailSubject: getMailSubject(docType, mailType),
        mailBodyMessage: getMailBody(docType, mailType)
    }

    const mailData = data.map((dat) => ({
        ...dat,
        url: getMailUrlDetail(docType, dat.docId),
        tableHeader: getMailTableHeader(docType, mailType),
    }));

    send(recipients, mailData, docType);
}

export const sendGeneralNotificationMail = async (mailRecipients: MailRecipients, data: MailTableData[]) => {
    // 
    if (!data || data.length === 0) {
        throw new Error(`general notification mail data cannot be empty`);
    }

    const mailType: TypeOf<typeof MailType> = "GENERAL_NOTIFICATION";
    const docType = "GENERAL_NOTIFICATION";

    const recipients: MailRecipients & MailContent = {
        ...mailRecipients,
        mailTitle: getMailTitle(docType, mailType),
        mailSubject: getMailSubject(docType, mailType),
        mailBodyMessage: getMailBody(docType, mailType)
    }
    // docTypeToName(item.documentType!),
    const mailData = data.map((dat) => ({
        ...dat,
        url: getMailUrlDetail(dat.docType!, dat.docId),
        tableHeader: getMailTableHeader(docType, mailType),
    }));

    send(recipients, mailData, docType);
}

const mailTemplate = (mailRecipients: MailRecipients & MailContent & MailHasSubmittedBy, data: MailTableData[], docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION") => {
    // 
    var mailTemplate = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">' +
        '<html dir="ltr" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">' +
        '<head>' +
        '<meta charset="UTF-8">' +
        '<meta content="width=device-width, initial-scale=1" name="viewport">' +
        '<meta name="x-apple-disable-message-reformatting">' +
        '<meta http-equiv="X-UA-Compatible" content="IE=edge">' +
        '<meta content="telephone=no" name="format-detection">' +
        '<title>New email template 2024-08-29</title><!--[if (mso 16)]>' +
        '<style type="text/css">' +
        'a {text-decoration: none;}' +
        '</style>' +
        '<![endif]--><!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]--><!--[if gte mso 9]>' +
        '<noscript>' +
        '<xml>' +
        '<o:OfficeDocumentSettings>' +
        '<o:AllowPNG></o:AllowPNG>' +
        '<o:PixelsPerInch>96</o:PixelsPerInch>' +
        '</o:OfficeDocumentSettings>' +
        '</xml>' +
        '</noscript>' +
        '<![endif]--><!--[if !mso]><!---->' +
        '<link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap" rel="stylesheet">' +
        '<link href="https://fonts.googleapis.com/css2?family=Manrope&display=swap" rel="stylesheet"><!--<![endif]-->' +
        '<style type="text/css">' +
        '.rollover:hover .rollover-first {' +
        'max-height:0px!important;' +
        'display:none!important;' +
        '}' +
        '.rollover:hover .rollover-second {' +
        'max-height:none!important;' +
        'display:block!important;' +
        '}' +
        '.rollover span {' +
        'font-size:0px;' +
        '}' +
        'u + .body img ~ div div {' +
        'display:none;' +
        '}' +
        '#outlook a {' +
        'padding:0;' +
        '}' +
        'span.MsoHyperlink,' +
        'span.MsoHyperlinkFollowed {' +
        'color:inherit;' +
        'mso-style-priority:99;' +
        '}' +
        'a.es-button {' +
        'mso-style-priority:100!important;' +
        'text-decoration:none!important;' +
        '}' +
        'a[x-apple-data-detectors],' +
        '#MessageViewBody a {' +
        'color:inherit!important;' +
        'text-decoration:none!important;' +
        'font-size:inherit!important;' +
        'font-family:inherit!important;' +
        'font-weight:inherit!important;' +
        'line-height:inherit!important;' +
        '}' +
        '.es-desk-hidden {' +
        'display:none;' +
        'float:left;' +
        'overflow:hidden;' +
        'width:0;' +
        'max-height:0;' +
        'line-height:0;' +
        'mso-hide:all;' +
        '}' +
        '@media only screen and (max-width:600px) {.es-m-p0r { padding-right:0px!important } .es-p-default { } *[class="gmail-fix"] { display:none!important } p, a { line-height:150%!important } h1, h1 a { line-height:120%!important } h2, h2 a { line-height:120%!important } h3, h3 a { line-height:120%!important } h4, h4 a { line-height:120%!important } h5, h5 a { line-height:120%!important } h6, h6 a { line-height:120%!important } .es-header-body p { } .es-content-body p { } .es-footer-body p { } .es-infoblock p { } h1 { font-size:30px!important; text-align:center } h2 { font-size:24px!important; text-align:center } h3 { font-size:20px!important; text-align:center } h4 { font-size:24px!important; text-align:left } h5 { font-size:20px!important; text-align:left } h6 { font-size:16px!important; text-align:left } .es-header-body h1 a, .es-content-body h1 a, .es-footer-body h1 a { font-size:30px!important } .es-header-body h2 a, .es-content-body h2 a, .es-footer-body h2 a { font-size:24px!important } .es-header-body h3 a, .es-content-body h3 a, .es-footer-body h3 a { font-size:20px!important } .es-header-body h4 a, .es-content-body h4 a, .es-footer-body h4 a { font-size:24px!important } .es-header-body h5 a, .es-content-body h5 a, .es-footer-body h5 a { font-size:20px!important } .es-header-body h6 a, .es-content-body h6 a, .es-footer-body h6 a { font-size:16px!important } .es-menu td a { font-size:12px!important } .es-header-body p, .es-header-body a { font-size:12px!important } .es-content-body p, .es-content-body a { font-size:12px!important } .es-footer-body p, .es-footer-body a { font-size:12px!important } .es-infoblock p, .es-infoblock a { font-size:12px!important } .es-m-txt-c, .es-m-txt-c h1, .es-m-txt-c h2, .es-m-txt-c h3, .es-m-txt-c h4, .es-m-txt-c h5, .es-m-txt-c h6 { text-align:center!important } .es-m-txt-r, .es-m-txt-r h1, .es-m-txt-r h2, .es-m-txt-r h3, .es-m-txt-r h4, .es-m-txt-r h5, .es-m-txt-r h6 { text-align:right!important } .es-m-txt-j, .es-m-txt-j h1, .es-m-txt-j h2, .es-m-txt-j h3, .es-m-txt-j h4, .es-m-txt-j h5, .es-m-txt-j h6 { text-align:justify!important } .es-m-txt-l, .es-m-txt-l h1, .es-m-txt-l h2, .es-m-txt-l h3, .es-m-txt-l h4, .es-m-txt-l h5, .es-m-txt-l h6 { text-align:left!important } .es-m-txt-r img, .es-m-txt-c img, .es-m-txt-l img { display:inline!important } .es-m-txt-r .rollover:hover .rollover-second, .es-m-txt-c .rollover:hover .rollover-second, .es-m-txt-l .rollover:hover .rollover-second { display:inline!important } .es-m-txt-r .rollover span, .es-m-txt-c .rollover span, .es-m-txt-l .rollover span { line-height:0!important; font-size:0!important; display:block } .es-spacer { display:inline-table } a.es-button, button.es-button { font-size:18px!important; padding:10px 20px 10px 20px!important; line-height:120%!important } a.es-button, button.es-button, .es-button-border { display:inline-block!important } .es-m-fw, .es-m-fw.es-fw, .es-m-fw .es-button { display:block!important } .es-m-il, .es-m-il .es-button, .es-social, .es-social td, .es-menu { display:inline-block!important } .es-adaptive table, .es-left, .es-right { width:100%!important } .es-content table, .es-header table, .es-footer table, .es-content, .es-footer, .es-header { width:100%!important; max-width:600px!important } .adapt-img { width:100%!important; height:auto!important } .es-mobile-hidden, .es-hidden { display:none!important } .es-desk-hidden { width:auto!important; overflow:visible!important; float:none!important; max-height:inherit!important; line-height:inherit!important } tr.es-desk-hidden { display:table-row!important } table.es-desk-hidden { display:table!important } td.es-desk-menu-hidden { display:table-cell!important } .es-menu td { width:1%!important } table.es-table-not-adapt, .esd-block-html table { width:auto!important } .h-auto { height:auto!important } }' +
        '@media screen and (max-width:384px) {.mail-message-content { width:414px!important } }' +
        '</style>' +
        '</head>' +
        '<body data-new-gr-c-s-loaded="14.1193.0" class="body" style="width:100%;height:100%;padding:0;Margin:0">' +
        '<div dir="ltr" class="es-wrapper-color" lang="en" style="background-color:#FFFFFF"><!--[if gte mso 9]>' +
        '<v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">' +
        '<v:fill type="tile" color="#ffffff"></v:fill>' +
        '</v:background>' +
        '<![endif]-->' +
        '<table width="100%" cellspacing="0" cellpadding="0" class="es-wrapper" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;background-color:#FFFFFF">' +
        '<tr>' +
        '<td valign="top" style="padding:0;Margin:0">' +
        '<table cellpadding="0" cellspacing="0" align="center" class="es-content" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important">' +
        '<tr>' +
        '<td align="center" style="padding:0;Margin:0">' +
        '<table bgcolor="#ffffff" align="center" cellpadding="0" cellspacing="0" class="es-content-body" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:#ffffff;width:910px" role="none">' +
        '<tr>' +
        '<td align="left" bgcolor="#ffffff" style="padding:0;Margin:0;background-color:#ffffff">' +
        '<table width="100%" cellspacing="0" cellpadding="0" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td valign="top" align="center" class="es-m-p0r" style="padding:0;Margin:0;width:910px">' +
        '<table width="100%" cellspacing="0" cellpadding="0" role="presentation" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +

        // mail header img
        '<td align="left" style="padding:0;Margin:0;padding-top:20px;padding-bottom:20px;padding-left:20px;font-size:0"><img src="' + mailImg + '" alt="" width="239" height="71" style="display:block;font-size:14px;border:0;outline:none;text-decoration:none"></td>' +
        // mail header img

        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '<tr>' +
        '<td align="left" style="padding:0;Margin:0;padding-left:20px;padding-top:30px;padding-right:20px">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td align="center" valign="top" style="padding:0;Margin:0;width:870px">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td align="center" style="padding:0;Margin:0">' +

        // mail title
        '<h1 style="Margin:0;font-family:Manrope, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:30px;font-style:normal;font-weight:bold;line-height:36px;color:#44465F">' + mailRecipients.mailTitle + '</h1>' +
        // mail title

        '</td>' +
        '</tr>' +
        '<tr>' +
        '<td align="left" style="padding:0;Margin:0;padding-top:15px">' +

        // salutation recipient with name
        '<p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px">Dear Mr./Mrs. ' + ucwords(mailRecipients.sendToUserName) + '</p>' +
        '<p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px"><br></p>' +
        // salutation recipient with name

        // mail body with or without table
        '<p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px">' + mailRecipients.mailBodyMessage + '</p>' +
        '</td>' +
        '</tr>' +
        '</table></td>' +
        ' </tr>' +
        '</table></td>' +
        '</tr>' +
        '<tr>' +
        '<td align="left" class="esdev-adapt-off" style="padding:20px;Margin:0">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td align="center" valign="top" style="padding:0;Margin:0;width:870px">' +
        '<table cellpadding="0" cellspacing="0" width="100%" bgcolor="#ffffff" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:separate;border-spacing:0px;background-color:#ffffff;border-radius:10px" role="presentation">' +
        '<tr>' +
        '<td align="center" style="padding:0;Margin:0">' +
        '<table border="1" bordercolor="#cccccc" align="center" cellspacing="10" cellpadding="10" class="es-table" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;height:100px;width:700px" role="presentation">' +
        '<tr style="background-color:#2A5583;height:50px">';

    data[0].tableHeader!.forEach((header) => {
        mailTemplate += '<td style="padding:0;Margin:0;font-family:tahoma, verdana, segoe, sans-serif;text-align:center;font-size:14px;line-height:16px !important;border-style:solid;color:#333333"><h6 style="Margin:0;font-family:Manrope, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:16px;font-style:normal;font-weight:normal;line-height:19.2px;color:#ffffff"><strong>' + header + '</strong></h6></td>';
    });

    mailTemplate += '</tr>';

    // mail body with or without table
    mailTemplate += getMailTableBody(docType, data);

    mailTemplate += '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '<tr>' +
        '<td align="left" style="padding:0;Margin:0;padding-left:20px;padding-right:20px;padding-bottom:40px">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td align="center" valign="top" style="padding:0;Margin:0;width:870px">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td align="left" style="padding:0;Margin:0;padding-top:15px">' +

        // closing
        '<p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px">Thank you for your attention and cooperation.</p>' +
        // closing

        // signature
        '<p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px"><br></p><p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px">Best regards,</p>' +
        '<p style="Margin:0;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;line-height:21px;letter-spacing:0;color:#44465F;font-size:14px">PBR Department</p>' +
        // signature

        '</td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table>' +

        '<table cellpadding="0" cellspacing="0" align="center" class="es-footer" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important;background-color:#A797F2;background-repeat:repeat;background-position:center top">' +
        '<tr>' +
        '<td align="center" bgcolor="#ffffff" style="padding:0;Margin:0;background-color:#ffffff">' +
        '<table bgcolor="#ffffff" align="center" cellpadding="0" cellspacing="0" class="es-footer-body" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:#ffffff;width:910px" role="none">' +
        '<tr>' +
        '<td align="left" bgcolor="#ffffff" style="padding:20px;Margin:0;background-color:#ffffff">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="none" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +
        '<td align="left" style="padding:0;Margin:0;width:870px">' +
        '<table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">' +
        '<tr>' +

        // mail footer img
        '<td align="center" class="es-infoblock made_with" style="padding:0;Margin:0;font-size:0"><img src="' + mailImg + '" alt="" width="125" style="display:block;font-size:14px;border:0;outline:none;text-decoration:none" height="37"></td>' +
        // mail footer img

        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table></td>' +
        '</tr>' +
        '</table>' +
        '</div>' +
        '</body>' +
        '</html>';

    return mailTemplate;
}

const getMailTableBody = (docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION", data: MailTableData[]) => {
    // 
    let mailTemplate = '';

    data.forEach(dat => {
        // 
        if (docType === "GENERAL_NOTIFICATION") {
            mailTemplate = '<tr style="height:50px">' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333"><strong>' + docTypeToName(dat.docType!) + '</strong></td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.department + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.section + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.value + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333"><a target="_blank" href="' + baseUrlEmployee + dat.url + '" style="mso-line-height-rule:exactly;text-decoration:none;color:#2A5583;font-size:12px;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;line-height:12px !important">Go To Details</a></td>' +
                '</tr>';
        } else if (docType === "REQUISITION") {
            mailTemplate = '<tr style="height:50px">' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.department + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.section + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.title + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.value + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.quantity + '</td>' + // TKDN
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333"><a target="_blank" href="' + baseUrlEmployee + dat.url + '" style="mso-line-height-rule:exactly;text-decoration:none;color:#2A5583;font-size:12px;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;line-height:12px !important">Go To Details</a></td>' +
                '</tr>';
        } else {
            mailTemplate = '<tr style="height:50px">' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.department + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.section + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.quantity + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333">' + dat.value + '</td>' +
                '<td style="padding:0;Margin:0;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;font-size:12px;line-height:12px !important;border-style:solid;color:#333333"><a target="_blank" href="' + baseUrlEmployee + dat.url + '" style="mso-line-height-rule:exactly;text-decoration:none;color:#2A5583;font-size:12px;text-align:center;font-family:tahoma, verdana, segoe, sans-serif;line-height:12px !important">Go To Details</a></td>' +
                '</tr>';
        }
    });

    return mailTemplate;
}

const getMailSubject = (docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION", mailType: TypeOf<typeof MailType>) => {
    // 
    if (mailType === "APPROVAL") {
        // 
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "PRISA - Approval for Procurement Plan";
        } else if (docType === "REQUISITION") {
            return "PRISA - Approval for Requisition";
        }
    }

    if (mailType === "NOTIFICATION") {
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "PRISA - Notification for Procurement Plan";
        } else if (docType === "REQUISITION") {
            return "PRISA - Notification for Requisition";
        }
    }

    if (mailType === "SENDBACK") {
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "PRISA - Procurement Plan Send Back";
        } else if (docType === "REQUISITION") {
            return "PRISA - Requisition Send Back";
        }
    }
    
    if (mailType === "ASSIGNMENT") {
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "PRISA - Procurement Plan Assignment";
        } else if (docType === "REQUISITION") {
            return "PRISA - Requisition Assignment";
        }
    }
    
    // GENERAL
    return "Prisa - Approval List"; // TODO: value NaN
}

const getMailTitle = (docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION", mailType: TypeOf<typeof MailType>) => {
    // 
    if (mailType === "APPROVAL") {
        // 
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "Approval for Procurement Plan";
        } else if (docType === "REQUISITION") {
            return "Approval for Requisition";
        }
    }

    if (mailType === "NOTIFICATION") {
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "Notification for Procurement Plan";
        } else if (docType === "REQUISITION") {
            return "Notification for Requisition";
        }
    }

    if (mailType === "SENDBACK") {
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "Procurement Plan Send Back";
        } else if (docType === "REQUISITION") {
            return "Requisition Send Back";
        }
    }
    
    if (mailType === "ASSIGNMENT") {
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "Procurement Plan Assignment";
        } else if (docType === "REQUISITION") {
            return "Requisition Assignment";
        }
    }

    // GENERAL
    return "Prisa Approval List";
}

const getMailBody = (docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION", mailType: TypeOf<typeof MailType>, submittedByUserName?: string): string => {
    // 
    if (mailType === "APPROVAL") {
        // 
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return "The following Procurement Plan require your review and approval:";
        } else if (docType === "REQUISITION") {
            return "The following Requisition require your review and approval:";
        }
    }

    if (mailType === "NOTIFICATION") {
        return "The following list has been submitted by Mr./Mrs. " + ucwords(submittedByUserName!) + ":";
    }

    if (mailType === "SENDBACK") {
        return "The following list has been has been sent back:";
    }

    if (mailType === "ASSIGNMENT") {
        return "The following list has been assigned by Mr./Mrs. " + ucwords(submittedByUserName!) + ":";
    }

    // GENERAL
    return "The following list require your review and approval:";
}

const getMailTableHeader = (docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION", mailType: TypeOf<typeof MailType>): string[] => {
    // 
    if (mailType === "APPROVAL") {
        // 
        if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
            return ["Department", "Section", "Quantity", "Value (USD)", "Link"];
        } else if (docType === "REQUISITION") {
            return ["Department", "Section", "Title", "Value (USD)", "% LCL / TKDN", "Link"];
        }
    }

    if (mailType === "NOTIFICATION" || mailType === "ASSIGNMENT") {
        if (docType === "REQUISITION") {
            return ["Department", "Section", "Title", "Value (USD)", "% LCL / TKDN", "Link"];
        }

        return ["Department", "Section", "Quantity", "Value (USD)", "Link"];
    }

    if (mailType === "SENDBACK") {
        if (docType === "REQUISITION") {
            return ["Department", "Section", "Title", "Value (USD)", "% LCL / TKDN", "Link"];
        }

        return ["Department", "Section", "Quantity", "Value (USD)", "Link"];
    }

    // GENERAL
    return ["Type", "Department", "Section", "Value (USD)", "Link"];
}

// docId = id, section, department
const getMailUrlDetail = (docType: TypeOf<typeof DocumentTemplate> | "GENERAL_NOTIFICATION", docId: string): string => {
    if (docType === "PROC_PLAN_UPP") {
        return "/procurement-plan/request/upp?section=" + docId;
    }
    else if (docType === "PROC_PLAN_APP") {
        return "/procurement-plan/request/app?department=" + docId;
    }
    else if (docType === "REQUISITION") {
        return "/requisition/request/detail?documentId=" + docId;
    }

    return "";
}

export const MailType = [
    "GENERAL_NOTIFICATION",
    "NOTIFICATION",
    "APPROVAL",
    "SENDBACK",
    "ASSIGNMENT",
] as const;

export type MailRecipients = {
    sendToUserMail: string;
    sendToUserName: string;
    ccUserMail?: string | any[];
}

export type MailContent = {
    mailSubject?: string;
    mailTitle?: string;
    mailBodyMessage?: string;
}

export type MailHasSubmittedBy = {
    submittedByUserName?: string;
}

export type MailInfo = {
    sendToUserMail: string;
    sendToUserName: string;
    mailSubject: string;
    mailTitle: string;
}

export type MailNotificationInfo = {
    sendToUserMail: string;
    sendToUserName: string;
    submittedByUserName: string;
    mailSubject: string;
    mailTitle: string;
}

export type MailTableData = {
    docType?: TypeOf<typeof DocumentTemplate>;
    subDocType?: TypeOf<typeof SubDocumentRequisition> | null;
    department: string;
    section: string;
    quantity?: number;
    value: number | string;
    docId: string;
    title?: string;
    url?: string;
    tableHeader?: string[];
}

export type MailTemplateTableData = {
    department: string;
    section: string;
    quantity: number;
    value: number | string;
    url: string;
}

export type GeneralMailTemplateTableData = {
    description: string;
    department: string;
    section: string;
    quantity: number;
    value: number | string;
    url: string;
}

type MailOptions = {
    from: string; // sender address
    to: string; // list of receivers
    cc?: string | string[]; // list of cc mails
    subject: string; // Subject line
    html: string; // html body
}

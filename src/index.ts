import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import swaggerUi from "swagger-ui-express";
import cron from "node-cron";

import { DataSource } from "typeorm";
import cookieParser from "cookie-parser";

import { controllerCollection } from "./app/controller/_controller.js";
import { handleError, handleJWTAuth } from "./app/controller/_middleware.js";
import { DatabaseTables, gateways } from "./app/gatewayimpl/_gateway.js";
import { usecases } from "./app/usecase/_usecase.js";
import { constructDeclarativeController, middlewareContextWithTraceId, printController } from "./framework/controller_express.js";
import { bootstrap, createContext } from "./framework/core.js";
import { transactionMiddleware } from "./framework/gateway_typeorm.js";

import { handleFileOperation } from "./app/controller/handle_file_operation.js";
import { getJSONName, handleLoginMicrosoft } from "./app/controller/handle_login_microsoft.js";
import { handleLoginSimple } from "./app/controller/handle_login_simple.js";
import { recordingInit, recordingMiddleware } from "./plugin/recording/recording.js";

import "reflect-metadata";
import { groupingControllerWithTag } from "./plugin/controller_ui/group_controller.js";
import { controllerToOpenAPI } from "./plugin/openapi/controller_to_openapi.js";
import { controllerVendorPQ } from "./app/controller/controller_vendor_pq.js";
import { handleLoginVendor } from "./app/controller/handle_login_vendor.js";
import { implFindProcPlanDetailByFromExcludeDepartment, implFindProcPlanDetailByRbtb } from "./app/gatewayimpl/impl_procplan.js";
import { InitDefaultVendorAccessApps, implFindOneVendor } from "./app/gatewayimpl/impl_vendor.js";
import { InitDefaultApprovalTemplateGroups } from "./app/gatewayimpl/impl_approval_template_group.js";
import { handleFileOperationVendor } from "./app/controller/handle_file_operation_vendor.js";
import { printMember } from "./app/utility/print_user.js";
import { sendMail } from "./app/utility/mailer.js";
import nodemailer, { SentMessageInfo } from "nodemailer";
import { scheduledCronJobs } from "./app/cron/cron.js";
import { getVendorDataCIVD } from "./app/utility/civd_vendor.js";
import { migrate } from "./app/utility/migration_user.js";
import { veraLogin } from "./app/utility/vera_vendor.js";
import { handleFilePublic } from "./app/controller/handle_file_public.js";
import { readWorksheets } from "./app/utility/worksheets.js";

dotenv.config();

export const getDatasource = () => {
  return new DataSource({
    type: "postgres",
    port: 5432,
    host: process.env.DATABASE_EPROC_HOST,
    username: process.env.DATABASE_EPROC_USER,
    password: process.env.DATABASE_EPROC_PASS,
    database: process.env.DATABASE_EPROC_NAME,
    synchronize: true,
    connectTimeoutMS: 10000,
    logging: false,
    entities: DatabaseTables,
    // entities: [
    //   //
    //   "src/app/gatewayimpl/table_*{.js,.ts}",
    //   "src/plugin/recording/recording_typeorm{.js,.ts}",
    // ],
    // migrations: ["src/migrations/*{.js,.ts}"],
  });
};

export const nodemailerTranport = () => {
  if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
    return {
      host: "sandbox.smtp.mailtrap.io", // Mailtrap
      // port: 587,
      port: 2525,
      secure: false,
      auth: {
        user: "8b1b5d8dcce548",
        pass: "ac87d777f4ca2e",
      },
      logger: false, // Logs connection issues
      debug: false, // Enables debug mode
    };
  } else {
    return {
      host: "hcml-co-id.mail.protection.outlook.com", // Prisa
      port: 25,
      secure: false,
      secureConnection: false,
      requireTLS: true,
      tls: { ciphers: "SSLv3", rejectUnauthorized: false },
    };
  }
};

export const main = async () => {
  //

  const isDevMode = true;

  try {
    //

    const ds = getDatasource();

    const mainRouter = express.Router();

    const vendorRouter = express.Router();

    const loginVendorRouter = express.Router();

    const loginRouter = express.Router();

    const publicRouter = express.Router();

    // collect all controller for Dependency Injection
    const controllers = [
      //
      ...controllerCollection.map((httpData) => constructDeclarativeController(mainRouter, httpData)),
      ...controllerVendorPQ.map((httpData) => constructDeclarativeController(vendorRouter, httpData)),
      handleFileOperation(mainRouter, ds),
      handleFileOperationVendor(vendorRouter),
      handleFilePublic(publicRouter),
      handleLoginMicrosoft(loginRouter),
      handleLoginVendor(process.env.TOKEN_SECRET_KEY_VENDOR as string, loginVendorRouter),
    ];

    // Login for development mode only
    isDevMode && controllers.push(handleLoginSimple(process.env.TOKEN_SECRET_KEY as string, loginRouter));

    const usecaseWithGatewayInstance = bootstrap(
      //
      gateways(ds),
      usecases,
      controllers,
      transactionMiddleware(ds),
      [recordingMiddleware()]
    );

    const app = express();

    app.use(cookieParser());
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    app.use(cors({ credentials: true, exposedHeaders: ["Trace-Id", "Date"] }));
    app.use(middlewareContextWithTraceId());

    if (isDevMode) {
      //
      // RECORDING
      app.use("/recording", recordingInit(ds, usecaseWithGatewayInstance));

      // CONTROLLER_UI
      app.use("/controllers", (req, res) =>
        res.json(
          groupingControllerWithTag([
            { prefix: "/api/v1", httpDatas: controllerCollection },
            { prefix: "/vendorapi/v1", httpDatas: controllerVendorPQ },
          ])
        )
      );

      // OPEN_API
      const openApiObj = controllerToOpenAPI(
        {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
          },
          bearerAuthVendor: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
          },
        },
        [
          { prefix: "/api/v1", httpDatas: controllerCollection },
          { prefix: "/vendorapi/v1", httpDatas: controllerVendorPQ },
        ]
      );
      app.use("/openapi", (req, res) => res.json(openApiObj));

      // SWAGGER_UI
      app.use("/swagger", swaggerUi.serve, swaggerUi.setup(openApiObj));
    }

    app.use("/public/v1", publicRouter);
    app.use(loginRouter);
    app.use("/vendorauth", loginVendorRouter);
    app.use("/api/v1", handleJWTAuth(process.env.TOKEN_SECRET_KEY as string), mainRouter);
    app.use(
      "/vendorapi/v1",
      handleJWTAuth(process.env.TOKEN_SECRET_KEY_VENDOR as string), // custom middleware for vendor auth middleware
      vendorRouter
    );
    app.use(handleError());

    // initialize database
    await ds.initialize();

    // await readWorksheets(ds);
    // await InitDefaultApprovalTemplateGroups(ds); // migrate default approval template groups
    // await InitDefaultVendorAccessApps(ds); // update vendor access apps default value dummy

    // initialize cron jobs
    await scheduledCronJobs(ds);

    app.listen(process.env.SERVER_PORT, () => {
      //

      // printController([
      //   { prefix: "/api/v1", httpDatas: controllerCollection },
      //   { prefix: "/vendorapi/v1", httpDatas: controllerVendorPQ },
      // ]);

      console.log("openapi url :", `http://localhost:${process.env.SERVER_PORT}/openapi`);
      console.log("swagger url :", `http://localhost:${process.env.SERVER_PORT}/swagger`);
      console.log("server is running");
    });
  } catch (error: any) {
    console.log(error.message);
    throw error;
  }

  //
};

await main();

// const experimental = async () => {
//   const ds = getDatasource();
//   await ds.initialize();
//   const f = implFindProcPlanDetailByRbtb(ds);

//   const data = await f(getContext(), {
//     userId: "80131088",
//     year: 2024,
//   });

//   console.log(data);
// };

// await experimental();
